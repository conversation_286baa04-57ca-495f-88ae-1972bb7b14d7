/* Wiki Template Styles - CSS Grid Layout */

/* Main container using CSS Grid */
.wiki-template-container {
	display: grid;
	grid-template-columns: 2fr 1fr; /* 2 columns: 2/3 width for content, 1/3 for sidebar */
	grid-template-rows: 2fr 1fr; /* 2 rows: 2/3 height for top, 1/3 for bottom */
	gap: 1rem;
	width: 100%;
	min-height: 600px;
	padding: 1rem;
	box-sizing: border-box;
	font-family: var(--font-interface);
}

/* Div 1: Text Content - spans 2 columns by 3 rows (left side) */
.wiki-text-content {
	grid-column: 1 / 2; /* First column only */
	grid-row: 1 / 3; /* Spans both rows */
	padding: 1.5rem;
	background: var(--background-primary);
	border: 1px solid var(--background-modifier-border);
	border-radius: 8px;
	overflow-y: auto;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.wiki-text-content h1 {
	margin-top: 0;
	color: var(--text-accent);
	border-bottom: 2px solid var(--background-modifier-border);
	padding-bottom: 0.5rem;
	margin-bottom: 1rem;
}

.wiki-text-content h2 {
	color: var(--text-normal);
	margin-top: 1.5rem;
	margin-bottom: 0.75rem;
}

.wiki-text-content p {
	line-height: 1.6;
	margin-bottom: 1rem;
	color: var(--text-normal);
}

.wiki-text-content ul {
	margin-left: 1.5rem;
	margin-bottom: 1rem;
}

.wiki-text-content li {
	margin-bottom: 0.5rem;
	color: var(--text-normal);
}

/* Div 2: Image - spans 1 column by 2 rows (top-right) */
.wiki-image-section {
	grid-column: 2 / 3; /* Second column */
	grid-row: 1 / 2; /* First row */
	padding: 1rem;
	background: var(--background-primary);
	border: 1px solid var(--background-modifier-border);
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-placeholder {
	text-align: center;
	padding: 2rem;
	background: var(--background-secondary);
	border: 2px dashed var(--background-modifier-border);
	border-radius: 8px;
	width: 100%;
	transition: all 0.3s ease;
}

.image-placeholder:hover {
	border-color: var(--text-accent);
	background: var(--background-modifier-hover);
}

.image-icon {
	font-size: 3rem;
	margin-bottom: 0.5rem;
	opacity: 0.6;
}

.image-placeholder p {
	margin: 0.5rem 0;
	font-weight: 600;
	color: var(--text-normal);
}

.image-placeholder small {
	color: var(--text-muted);
	font-size: 0.85rem;
}

/* Div 3: Table - spans 1 column by 1 row (bottom-right) */
.wiki-table-section {
	grid-column: 2 / 3; /* Second column */
	grid-row: 2 / 3; /* Second row */
	padding: 1rem;
	background: var(--background-primary);
	border: 1px solid var(--background-modifier-border);
	border-radius: 8px;
	overflow-y: auto;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.wiki-table-section h3 {
	margin-top: 0;
	margin-bottom: 1rem;
	color: var(--text-accent);
	font-size: 1.1rem;
	border-bottom: 1px solid var(--background-modifier-border);
	padding-bottom: 0.5rem;
}

.info-table {
	width: 100%;
	border-collapse: collapse;
	font-size: 0.9rem;
}

.info-table td {
	padding: 0.5rem 0.75rem;
	border-bottom: 1px solid var(--background-modifier-border);
	vertical-align: top;
}

.info-table td:first-child {
	width: 40%;
	color: var(--text-muted);
	font-weight: 500;
}

.info-table td:last-child {
	color: var(--text-normal);
}

.info-table tr:last-child td {
	border-bottom: none;
}

.info-table tr:hover {
	background: var(--background-modifier-hover);
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
	.wiki-template-container {
		grid-template-columns: 1fr;
		grid-template-rows: auto auto auto;
		gap: 1rem;
	}

	.wiki-text-content {
		grid-column: 1 / 2;
		grid-row: 1 / 2;
	}

	.wiki-image-section {
		grid-column: 1 / 2;
		grid-row: 2 / 3;
	}

	.wiki-table-section {
		grid-column: 1 / 2;
		grid-row: 3 / 4;
	}
}
