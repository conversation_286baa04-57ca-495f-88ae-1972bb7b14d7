import * as React from "react";

interface ModalContentProps {
	onBlankClick?: () => void;
	onWikiTemplateClick?: () => void;
}

export const ModalContent: React.FC<ModalContentProps> = ({
	onBlankClick,
	onWikiTemplateClick,
}) => {
	const handleBlankClick = () => {
		console.log("Blank button clicked");
		onBlankClick?.();
	};

	const handleWikiTemplateClick = () => {
		console.log("Wiki Template button clicked");
		onWikiTemplateClick?.();
	};

	const containerStyle: React.CSSProperties = {
		display: "flex",
		flexDirection: "column",
		alignItems: "center",
		gap: "10px",
		padding: "20px",
	};

	const titleStyle: React.CSSProperties = {
		marginBottom: "20px",
	};

	const buttonContainerStyle: React.CSSProperties = {
		display: "flex",
		gap: "15px",
		justifyContent: "center",
	};

	const buttonStyle: React.CSSProperties = {
		padding: "10px 20px",
		fontSize: "14px",
		cursor: "pointer",
	};

	return (
		<div style={containerStyle}>
			<h3 style={titleStyle}>Fortellr</h3>
			<div style={buttonContainerStyle}>
				<button style={buttonStyle} onClick={handleBlankClick}>
					Blank Note
				</button>
				<button style={buttonStyle} onClick={handleWikiTemplateClick}>
					Wiki Template
				</button>
			</div>
		</div>
	);
};
