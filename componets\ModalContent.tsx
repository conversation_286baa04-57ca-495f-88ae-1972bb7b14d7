import React from "react";

interface ModalContentProps {
	onBlackClick?: () => void;
	onTemplate1Click?: () => void;
}

export const ModalContent: React.FC<ModalContentProps> = ({
	onBlankClick,
	onTemplate1Click,
}) => {
	const handleBlankClick = () => {
		console.log("Black button clicked");
		onBlankClick?.();
	};

	const handleTemplate1Click = () => {
		console.log("Template 1 button clicked");
		onTemplate1Click?.();
	};

	const containerStyle: React.CSSProperties = {
		display: "flex",
		flexDirection: "column",
		alignItems: "center",
		gap: "10px",
		padding: "20px",
	};

	const titleStyle: React.CSSProperties = {
		marginBottom: "20px",
	};

	const buttonContainerStyle: React.CSSProperties = {
		display: "flex",
		gap: "15px",
		justifyContent: "center",
	};

	const buttonStyle: React.CSSProperties = {
		padding: "10px 20px",
		fontSize: "14px",
		cursor: "pointer",
	};

	return (
		<div style={containerStyle}>
			<h3 style={titleStyle}>Fortellr</h3>
			<div style={buttonContainerStyle}>
				<button style={buttonStyle} onClick={handleBlankClick}>
					blank
				</button>
				<button style={buttonStyle} onClick={handleTemplate1Click}>
					template 1
				</button>
			</div>
		</div>
	);
};
