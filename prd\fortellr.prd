---

## **Product Requirements Document: Wiki Page Layout Plugin**

### 1. Introduction & Vision

**Problem:** Creating structured, multi-column layouts in Obsidian (like a wiki page with sidebars and info boxes) requires manual and repetitive setup using complex markdown, HTML, and/or CSS snippets for each new note.

**Vision:** To provide users with a one-click solution to generate a pre-defined, wiki-style page layout, simplifying the creation of visually organized notes.

**Project Name:** Obsidian Layout Creator (or Wiki Page Plugin)

### 2. Target Audience

Obsidian users who want to create structured notes without the friction of manual setup. This includes:
*   World-builders
*   TTRPG Game Masters
*   Researchers
*   Anyone wanting to create summary pages or dashboards.

### 3. Core Features & User Stories

The Minimum Viable Product (MVP) will focus on implementing the single layout provided in the image.

**User Story 1: Choosing a Page Type**
*   **As a user,** when I create a new note,
*   **I want** to be presented with a choice between a standard blank note and a "Wiki Page" layout,
*   **So that** I can instantly start with the structure I need.

**User Story 2: Generating the Wiki Layout**
*   **As a user,** when I select the "Wiki Page" option,
*   **I want** a new note to be created and pre-populated with the necessary code to render the layout,
*   **So that** I don't have to build it from scratch.

### 4. Functional Requirements

#### 4.1. New Note Modal
*   The plugin must intercept the standard "Create New File" command.
*   Upon activation, a modal window must appear.
*   The modal will contain two distinct options (e.g., buttons):
    1.  **"Blank Page"**: This will execute Obsidian's default new note behavior.
    2.  **"Wiki Page"**: This will trigger the layout generation.

#### 4.2. Wiki Page Template
*   Selecting "Wiki Page" creates a new `.md` file.
*   The file will be populated with a pre-defined template.
*   The template will consist of HTML `<div>`s and placeholder text to create the following structure:
    *   **Main Container:** A wrapper for the entire layout.
    *   **Left Sidebar:** A narrow, collapsible column on the left.
        *   Must include a toggle icon (`<`) to collapse/expand.
        *   Placeholder content: "Side bar".
    *   **Main Content Area:** A large central column.
        *   Placeholder content: "Textbox".
    *   **Right Column:** A column on the right containing two stacked boxes.
        *   Top box placeholder: "Image".
        *   Bottom box placeholder: "Table".
*   The necessary CSS to style these `<div>`s (using **Flexbox** or **Grid**) must be included, either within a `<style>` tag in the note itself or, preferably, injected by the plugin via a registered CSS snippet.

### 5. Technical Implementation Pointers

*   **Framework:** Obsidian Plugin API.
*   **Triggering the Modal:** Use the Obsidian API to add a command that opens a `Modal`. You can then replace the default "new note" hotkey or add a new ribbon icon to trigger your command.
    *   `this.addCommand({ id: 'create-layout-note', name: 'Create new note with layout', ... })`
*   **Modal UI:** Use the `Modal` class from the Obsidian API (`import { App, Modal, ... } from 'obsidian';`).
*   **Layout Structure:** The template content will be a string of HTML and Markdown stored within your plugin's code.
    *   **Example HTML Structure (to be inserted into the new note):**
        ```html
        <div class="wiki-layout-container">
          <div class="wiki-left-sidebar">
            <!-- Sidebar content -->
            Side bar
          </div>
          <div class="wiki-main-content">
            <!-- Main content -->
            Textbox
          </div>
          <div class="wiki-right-column">
            <div class="wiki-right-box">Image</div>
            <div class="wiki-right-box">Table</div>
          </div>
        </div>
        ```
*   **Styling (CSS):** Use CSS Flexbox for laying out the columns. This CSS should be loaded by the plugin into Obsidian.
*   **File Creation:** Use the Vault API to create and write to the new file:
    *   `this.app.vault.create('Untitled.md', templateString);`

### 6. Out of Scope for V1 (Future Enhancements)
*   User-defined custom templates.
*   A visual editor for creating new layouts.
*   More than one pre-defined layout.
*   Automatically populating the boxes with content (e.g., via transclusion).