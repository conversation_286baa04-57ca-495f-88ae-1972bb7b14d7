import * as React from "react";

export interface WikiTemplateProps {
	className?: string;
}

export const WikiTemplate: React.FC<WikiTemplateProps> = ({
	className = "",
}) => {
	return (
		<div className={`wiki-template-container ${className}`}>
			{/* Div 1: Text Content - spans 2 columns by 3 rows (left side) */}
			<div className="wiki-text-content">
				<h1>Main Content Area</h1>
				<p>
					This is the main text content area that spans two columns
					wide and three rows tall. You can add your primary content
					here, including paragraphs, lists, and other text elements.
				</p>
				<p>
					Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed
					do eiusmod tempor incididunt ut labore et dolore magna
					aliqua. Ut enim ad minim veniam, quis nostrud exercitation
					ullamco laboris nisi ut aliquip ex ea commodo consequat.
				</p>
				<h2>Subsection</h2>
				<p>
					Duis aute irure dolor in reprehenderit in voluptate velit
					esse cillum dolore eu fugiat nulla pariatur. Excepteur sint
					occaecat cupidatat non proident, sunt in culpa qui officia
					deserunt mollit anim id est laborum.
				</p>
				<ul>
					<li>Feature one</li>
					<li>Feature two</li>
					<li>Feature three</li>
				</ul>
			</div>

			{/* Div 2: Image - spans 1 column by 2 rows (top-right) */}
			<div className="wiki-image-section">
				<div className="image-placeholder">
					<div className="image-icon">🖼️</div>
					<p>Image Placeholder</p>
					<small>Replace with your image</small>
				</div>
			</div>

			{/* Div 3: Table - spans 1 column by 1 row (bottom-right) */}
			<div className="wiki-table-section">
				<h3>Quick Info</h3>
				<table className="info-table">
					<tbody>
						<tr>
							<td>
								<strong>Type:</strong>
							</td>
							<td>Example</td>
						</tr>
						<tr>
							<td>
								<strong>Status:</strong>
							</td>
							<td>Active</td>
						</tr>
						<tr>
							<td>
								<strong>Created:</strong>
							</td>
							<td>2025-01-13</td>
						</tr>
						<tr>
							<td>
								<strong>Category:</strong>
							</td>
							<td>Template</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	);
};

export default WikiTemplate;
