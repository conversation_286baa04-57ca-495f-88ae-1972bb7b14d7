/**
 * Generates the HTML content for the wiki-style template
 * This function creates the HTML structure that will be inserted into a new Obsidian note
 */
export function generateWikiTemplateHTML(): string {
  return `<div class="wiki-template-container">
  <!-- Main Text Content Area (Left side, spans 2 columns by 3 rows) -->
  <div class="wiki-text-content">
    <h1>Main Content Area</h1>
    <p>
      This is the main text content area that spans two columns wide and three rows tall. 
      You can add your primary content here, including paragraphs, lists, and other text elements.
    </p>
    <p>
      Replace this placeholder text with your actual content. You can include:
    </p>
    <ul>
      <li>Detailed descriptions</li>
      <li>Background information</li>
      <li>Key features or characteristics</li>
      <li>Related links and references</li>
    </ul>
    
    <h2>Additional Section</h2>
    <p>
      Add more sections as needed for your content. This layout provides a clean, 
      organized structure for wiki-style pages.
    </p>
  </div>

  <!-- Image Section (Top-right, spans 1 column by 2 rows) -->
  <div class="wiki-image-section">
    <div class="image-placeholder">
      <div class="image-icon">🖼️</div>
      <p>Image Placeholder</p>
      <small>Replace with your image using: ![Alt text](image-url)</small>
    </div>
  </div>

  <!-- Table Section (Bottom-right, spans 1 column by 1 row) -->
  <div class="wiki-table-section">
    <h3>Quick Info</h3>
    <table class="info-table">
      <tbody>
        <tr>
          <td><strong>Type:</strong></td>
          <td>Example</td>
        </tr>
        <tr>
          <td><strong>Status:</strong></td>
          <td>Active</td>
        </tr>
        <tr>
          <td><strong>Created:</strong></td>
          <td>${new Date().toISOString().split('T')[0]}</td>
        </tr>
        <tr>
          <td><strong>Category:</strong></td>
          <td>Template</td>
        </tr>
        <tr>
          <td><strong>Tags:</strong></td>
          <td>#wiki #template</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

---

## Additional Notes

You can add more content below the wiki template layout. This section will appear below the grid layout and can contain:

- Additional text content
- More detailed information
- References and links
- Tags and metadata

The wiki template above uses CSS Grid to create a responsive, organized layout perfect for character sheets, location descriptions, item catalogs, or any structured information.`;
}

/**
 * Generates a simplified markdown version of the wiki template
 * This version uses markdown tables and basic formatting
 */
export function generateWikiTemplateMarkdown(): string {
  return `# Main Content Area

This is the main text content area. You can add your primary content here, including paragraphs, lists, and other text elements.

Replace this placeholder text with your actual content. You can include:

- Detailed descriptions
- Background information  
- Key features or characteristics
- Related links and references

## Additional Section

Add more sections as needed for your content. This layout provides a clean, organized structure for wiki-style pages.

---

## Quick Info

| Field | Value |
|-------|-------|
| **Type** | Example |
| **Status** | Active |
| **Created** | ${new Date().toISOString().split('T')[0]} |
| **Category** | Template |
| **Tags** | #wiki #template |

---

## Image Section

![Image Placeholder](image-url-here)
*Replace with your actual image*

---

## Additional Notes

You can add more content here. This section can contain:

- Additional text content
- More detailed information
- References and links
- Tags and metadata`;
}
