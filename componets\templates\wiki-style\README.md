# Wiki-Style Template Component

This directory contains a React/TypeScript component that creates a wiki-style layout using CSS Grid. The layout is designed to mimic traditional wiki pages with a main content area, image section, and information table.

## Layout Structure

The wiki template uses a **3-column by 3-row CSS Grid** layout with the following specifications:

### Grid Layout
```
┌─────────────────┬─────────────┐
│                 │             │
│   Text Content  │    Image    │
│   (2 cols x     │  (1 col x   │
│    3 rows)      │   2 rows)   │
│                 │             │
│                 ├─────────────┤
│                 │    Table    │
│                 │  (1 col x   │
│                 │   1 row)    │
└─────────────────┴─────────────┘
```

### Sections

1. **Text Content (Left)**: Spans 2 columns by 3 rows
   - Main content area for paragraphs, headings, lists
   - Scrollable if content overflows
   - Supports full markdown/HTML content

2. **Image Section (Top-Right)**: Spans 1 column by 2 rows
   - Placeholder for images
   - Centered content with hover effects
   - Easy to replace with actual images

3. **Table Section (Bottom-Right)**: Spans 1 column by 1 row
   - Quick information table
   - Key-value pairs for metadata
   - Hover effects on rows

## Files

- `wiki-template.tsx` - React component
- `styles.css` - CSS Grid layout and styling
- `template-generator.ts` - Functions to generate HTML/Markdown content
- `index.ts` - Export file for easy importing
- `README.md` - This documentation file

## Usage

### As a React Component

```tsx
import { WikiTemplate } from './components/templates/wiki-style';

// Use in your React component
<WikiTemplate className="custom-class" />
```

### As Generated Content

```typescript
import { generateWikiTemplateHTML } from './components/templates/wiki-style/template-generator';

// Generate HTML content for a new note
const content = generateWikiTemplateHTML();
```

## Customization

### CSS Variables Used

The component uses Obsidian's CSS variables for theming:

- `--background-primary` - Main background color
- `--background-secondary` - Secondary background color
- `--background-modifier-border` - Border colors
- `--background-modifier-hover` - Hover states
- `--text-normal` - Normal text color
- `--text-accent` - Accent text color
- `--text-muted` - Muted text color
- `--font-interface` - Interface font family

### Responsive Design

The layout automatically adapts to smaller screens:
- On screens < 768px width, switches to single-column layout
- Sections stack vertically: Text → Image → Table

## Integration with Obsidian Plugin

The template is integrated with the Fortellr plugin modal:

1. User clicks "Create new note with layout" command
2. Modal appears with "Blank Note" and "Wiki Template" options
3. Selecting "Wiki Template" creates a new note with the wiki layout
4. The generated content includes the HTML structure and placeholder content

## Styling Notes

- Uses CSS Grid for precise layout control
- Responsive design with mobile-first approach
- Consistent with Obsidian's design language
- Smooth transitions and hover effects
- Proper semantic HTML structure

## Future Enhancements

Potential improvements for future versions:

1. **Dynamic Content**: Allow users to specify content during creation
2. **Multiple Layouts**: Additional grid configurations (2x2, 4x4, etc.)
3. **Template Variants**: Different styles for different use cases
4. **Image Upload**: Direct image upload integration
5. **Table Customization**: Dynamic table rows and columns
6. **Export Options**: Export to different formats (PDF, HTML, etc.)

## Browser Compatibility

The CSS Grid layout is supported in:
- Chrome 57+
- Firefox 52+
- Safari 10.1+
- Edge 16+

Since Obsidian uses Electron (Chromium-based), compatibility is guaranteed.
