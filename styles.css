/*
Fortellr Plugin Styles

This CSS file will be included with your plugin, and
available in the app when your plugin is enabled.
*/

/* Import wiki template styles */
@import url('./componets/templates/wiki-style/styles.css');

/* General plugin styles */
.fortellr-plugin {
  font-family: var(--font-interface);
}

/* Modal styles */
.fortellr-modal .modal-content {
  padding: 1rem;
}

.fortellr-modal .modal-title {
  text-align: center;
  margin-bottom: 1rem;
  color: var(--text-accent);
}

.fortellr-modal .button-container {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.fortellr-modal .template-button {
  padding: 0.75rem 1.5rem;
  border: 1px solid var(--background-modifier-border);
  border-radius: 6px;
  background: var(--background-primary);
  color: var(--text-normal);
  cursor: pointer;
  transition: all 0.2s ease;
}

.fortellr-modal .template-button:hover {
  background: var(--background-modifier-hover);
  border-color: var(--text-accent);
}

.fortellr-modal .template-button:active {
  transform: translateY(1px);
}
