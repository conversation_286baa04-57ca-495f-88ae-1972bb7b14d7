import { App, Modal, Notice } from "obsidian";
import { StrictMode } from "react";
import { Root, createRoot } from "react-dom/client";
import { ModalContent } from "./ModalContent";
import { AppContext } from "./AppContext";

//modal for file creation options
export class FileCreateModal extends Modal {
	root: Root | null = null;

	constructor(app: App) {
		super(app);
	}

	async onOpen() {
		// Mount the React component
		this.root = createRoot(this.contentEl);
		this.root.render(
			<StrictMode>
				<AppContext.Provider value={this.app}>
					<ModalContent
						onBlankClick={() => this.handleBlankClick()}
						onTemplate1Click={() => this.handleTemplate1Click()}
					/>
				</AppContext.Provider>
			</StrictMode>
		);
	}

	async onClose() {
		// Clean up the React component
		this.root?.unmount();
	}

	private async handleBlankClick() {
		console.log("Blank button clicked from modal");
		// Generate a blank note

		try {
			//Generate a default filename
			const defaultFileName = "Untitled";
			let fileName = `${defaultFileName}.md`;
			let fileNumber = 1;

			// Check if file already exists and increment number if needed
			while (await this.app.vault.adapter.exists(fileName)) {
				fileName = `${defaultFileName} ${fileNumber}.md`;
				fileNumber++;
			}

			// Create an empty file
			const file = await this.app.vault.create(fileName, "");

			// Open the newly created file in the editor
			const leaf = this.app.workspace.getLeaf(false);
			await leaf.openFile(file);

			// Close the modal
			this.close();
		} catch (error) {
			console.error("Error creating blank note:", error);
			// Optionally show an error notice to the user
			new Notice("Failed to create new note. Please try again.");
		}
	}

	private handleTemplate1Click() {
		console.log("Template 1 button clicked from modal");
		// Add your logic here
		// You can access this.app for Obsidian API calls
	}
}
