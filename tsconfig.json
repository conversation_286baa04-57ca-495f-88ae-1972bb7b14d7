{"compilerOptions": {"forceConsistentCasingInFileNames": true, "jsx": "react-jsx", "strict": true, "baseUrl": ".", "inlineSourceMap": true, "inlineSources": true, "module": "ESNext", "target": "ES6", "allowJs": true, "noImplicitAny": true, "moduleResolution": "node", "importHelpers": true, "isolatedModules": true, "strictNullChecks": true, "lib": ["DOM", "ES5", "ES6", "ES7"]}, "include": ["**/*.ts", "**/*.tsx"]}